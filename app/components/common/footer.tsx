import { Link } from "react-router"
import { WhatsappIcon } from "@/components/icons/whatsapp-icon"
import { InstagramIcon } from "../icons"

const Footer = () => {
  return (
    <footer className="mx-auto grid w-full max-w-[90rem] grid-cols-12 gap-y-8 py-16 text-sm text-gray-300">
      <div className="col-span-12 flex flex-col items-center space-y-2 lg:col-span-4">
        {/* <div>Contact us</div> */}
        <div className="flex flex-col items-center lg:items-start">
          <div className="flex flex-col pb-2">Contact us</div>
          <div>T-175, Tlangnuam</div>
          <div>Aizawl, Mizoram</div>
          <div>+91-8910991892</div>
          <div><EMAIL></div>
          <div>796005</div>
        </div>
      </div>
      <div className="col-span-12 flex flex-col items-center space-y-2 lg:col-span-4">
        <div>Connect with us</div>
        <div className="flex gap-x-4 text-2xl">
          <a href="https://www.instagram.com/enila.in" target="_blank" rel="noreferrer noopener">
            <InstagramIcon />
          </a>
          <a href="https://wa.me/+918731875303" target="_blank" rel="noreferrer noopener">
            <WhatsappIcon />
          </a>
          {/* <FacebookIcon /> */}
          {/* <TwitterIcon /> */}
        </div>
        <div>©{new Date().getFullYear()} Enila. All rights reserved.</div>
      </div>
      <div className="col-span-12 flex flex-col items-center lg:col-span-4">
        <div className="flex flex-col items-center lg:items-end">
          <Link className="hover:underline" to="/privacy-policy">
            Privacy policy
          </Link>
          <Link className="hover:underline" to="/terms-and-conditions">
            Terms and conditions
          </Link>
          <Link className="hover:underline" to="/refund-policy">
            Refund policy
          </Link>
          <div className="flex gap-x-4">
            <a
              href="https://play.google.com/store/apps/details?id=in.enila.app&hl=en
"
              target="_blank"
              rel="noopener noreferrer"
              className="h-12 w-full bg-red-400"
            >
              Google store badge here
            </a>
            <a
              href="https://play.google.com/store/apps/details?id=in.enila.app&hl=en
"
              target="_blank"
              rel="noopener noreferrer"
              className="h-12 w-full bg-red-400"
            >
              Apple store badge here
            </a>
          </div>
        </div>
      </div>

      <div className="col-span-12 mb-4 flex flex-col items-center justify-center text-xs lg:mb-0">
        <img src="/arsi_logo.png" alt="Arsi logo" className="size-12" />
        <span>Made with love by</span>
        <a
          href="https://arsi.in"
          target="_blank"
          rel="noreferrer noopener"
          className="hover:underline"
        >
          Arsi Consultancy
        </a>
      </div>
    </footer>
  )
}

export default Footer

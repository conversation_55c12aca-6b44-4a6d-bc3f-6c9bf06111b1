import {
  type LoginSchemaType,
  type OtpSchemaType,
  loginSchema,
  otpSchema,
} from "@/lib/types"
import { useForm } from "react-hook-form"
import { useState } from "react"
import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { USER_LOGIN } from "@/lib/graphql/mutation"
import { graphqlClient } from "@/lib/utils"
import { ClientError } from "graphql-request"
import { toast } from "../ui/use-toast"
import { Label } from "../ui/label"
import { Input } from "../ui/input"
import { Button } from "../ui/button"
import { VerifyOtpDialog } from "."
import { useNavigate } from "react-router"
import { useMutation } from "@tanstack/react-query"

const MobileLogin = () => {
  const navigate = useNavigate()

  const [openOtp, setOpenOtp] = useState(false)
  const loginForm = useForm<LoginSchemaType>({
    resolver: zod<PERSON><PERSON><PERSON><PERSON>(loginSchema),
  })

  const otpForm = useForm<OtpSchemaType>({
    resolver: zodResolver(otpSchema),
  })

  const userLogin = useMutation({
    mutationFn: async (variables: LoginSchemaType) => {
      if (variables.credentials.includes("@")) {
        const data = await graphqlClient.request(USER_LOGIN, {
          email: variables.credentials,
        })
        return data
      } else {
        const data = await graphqlClient.request(USER_LOGIN, {
          mobileNumber: variables.credentials,
        })
        return data
      }
    },
    // Optional: You can define onSuccess, onError, onSettled, etc. here
    onSuccess: (data) => {
      if (data.userLogin?.otp_id) {
        navigate({
          search: "?ref=/m",
        })
        setOpenOtp(true)
        otpForm.setValue("otpId", data.userLogin.otp_id)
      }
    },
    onError: (error) => {
      if (error instanceof ClientError) {
        toast({
          description:
            error?.response?.errors?.[0]?.message || "Error validating otp",
        })
      }
    },
  })

  const handleLogin = (data: LoginSchemaType) => {
    void userLogin.mutate(data)
  }

  const handleDialogChange = (state: boolean) => {
    setOpenOtp(state)
  }

  return (
    <>
      <div className="mt-16 flex flex-col px-4">
        <h1 className="text-center text-2xl">Welcome</h1>
        <div className="text-center">Please login to continue</div>
        <form
          onSubmit={loginForm.handleSubmit(handleLogin)}
          className="mt-8 flex w-full flex-col space-y-2"
        >
          <Label>Mobile number / Email</Label>
          <Input {...loginForm.register("credentials")} />
          <p className="text-sm text-muted-foreground">
            Verify OTP with email for regions outside India.
          </p>
          <Button type="submit" isLoading={userLogin.isPending}>
            Continue
          </Button>
        </form>
      </div>
      <VerifyOtpDialog
        open={openOtp}
        handleDialogChange={handleDialogChange}
        form={otpForm}
        resendOtp={() => handleLogin(loginForm.getValues())}
      />
    </>
  )
}

export default MobileLogin

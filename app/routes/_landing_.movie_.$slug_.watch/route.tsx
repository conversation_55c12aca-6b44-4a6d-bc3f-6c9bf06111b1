import { WatchDurationContentType, WatchableContentTypes } from "@/gql/graphql"
import { GENERATE_CONTENT_URL, UPSERT_WATCHED_DURATION } from "@/lib/graphql/mutation"
import { baseUrl, getTokenFromUrl, graphqlClient } from "@/lib/utils"
import MuxPlayer from "@mux/mux-player-react"
import { useLoaderData } from "react-router"
import { useEffect, useRef } from "react"
import { Button } from "@/components/ui/button"
import { LeftIcon, MaximizeIcon } from "@/components/icons"
import { PageError, PageLoader } from "@/components/common"
import {
  MediaControlBar,
  MediaController,
  MediaLoadingIndicator,
  MediaMuteButton,
  MediaPlayButton,
  MediaSeekBackwardButton,
  MediaSeekForwardButton,
  MediaTimeDisplay,
  MediaTimeRange,
  MediaVolumeRange,
} from "media-chrome/react"

import {
  MediaSettingsMenu,
  MediaSettingsMenuButton,
  MediaSettingsMenuItem,
  MediaPlaybackRateMenu,
  MediaRenditionMenu,
  MediaCaptionsMenu,
  MediaCaptionsMenuButton,
} from "media-chrome/react/menu"

import { usePlayVideo } from "@/lib/hooks"
import type { ContinueWatchingType } from "@/lib/types"
import MuxPlayerElement from "@mux/mux-player"
import { CONTINUE_WATCHING_INTERVAL } from "@/lib/utils/constants"

import { useMutation, useQuery } from "@tanstack/react-query"
import { getSession } from "@/sessions"
import type { Route } from "./+types/route"
import MOVIE_BY_SLUG_QUERY from "@/lib/graphql/queries/movie-by-slug-query"

export async function loader({ params, request }: Route.LoaderArgs) {
  const session = await getSession(request.headers.get("Cookie"))

  const token = session.get("token")

  const response = await graphqlClient.request(
    MOVIE_BY_SLUG_QUERY,
    {
      slug: params.slug,
    },
    {
      Authorization: `Bearer ${token}`,
    }
  )

  const movie = response.getMovieBySlug

  if (movie) {
    return { movie }
  } else {
    throw new Error("404. Not Found")
  }
}

export const meta = ({ data, location }: Route.MetaArgs) => {
  return [
    { title: `Enila - ${data?.movie?.title}` },
    { name: "description", content: `${data?.movie?.description_en}` },
    {
      property: "og:title",
      content: `Enila - ${data?.movie?.title}`,
    },
    {
      property: "og:url",
      content: `https://enila.in${location.pathname}`,
    },
    {
      property: "og:type",
      content: "website",
    },
    {
      property: "og:image",
      itemProp: "image",
      content: data?.movie?.imageLandscape?.path
        ? `${baseUrl}/image/small/${data?.movie?.imageLandscape?.path}`
        : "",
    },
    {
      property: "og:image:height",
      content: "600",
    },
    {
      property: "og:image:width",
      content: "600",
    },
    {
      property: "robots",
      content: "index,follow",
    },
    {
      property: "og:description",
      content: `${data?.movie?.description_en}`,
    },
    {
      property: "twitter:card",
      content: "summary_large_image",
    },
    {
      property: "twitter:url",
      content: `https://enila.in${location.pathname}`,
    },
    {
      property: "twitter:title",
      content: `Enila - ${data?.movie?.title}`,
    },
    {
      property: "twitter:description",
      content: `${data?.movie?.description_en}`,
    },
    {
      property: "twitter:image",
      content: data?.movie?.imageLandscape?.path
        ? `${baseUrl}/image/small/${data?.movie?.imageLandscape?.path}`
        : "",
    },
    {
      property: "og:site_name",
      content: "Enila",
    },
  ]
}

const WatchMovie = () => {
  const { movie } = useLoaderData<typeof loader>()
  // const { slug } = useParams()

  const videoRef = useRef<MuxPlayerElement | null>(null)

  const { isLoading, handlePlay, handleBackButton, handleFullScreen } = usePlayVideo()

  const {
    data,
    isLoading: generating,
    isError,
  } = useQuery({
    queryKey: ["generate-content-movie-by-id", movie?.id],
    queryFn: async () => {
      return graphqlClient.request(GENERATE_CONTENT_URL, {
        id: movie.id,
        contentType: WatchableContentTypes.Movie,
      })
    },
    enabled: !!movie?.id,
  })

  const handleContinueWatching = useMutation({
    mutationFn: async (variables: ContinueWatchingType) =>
      await graphqlClient.request(UPSERT_WATCHED_DURATION, {
        contentId: variables.id,
        contentType: variables.contentType,
        duration: variables.duration,
        totalDuration: variables.totalDuration,
      }),
  })

  useEffect(() => {
    const progressInterval = setInterval(() => {
      const currentTime = videoRef?.current?.currentTime
      const totalDuration = videoRef?.current?.duration

      const isPlaying = !videoRef?.current?.paused

      if (isPlaying) {
        handleContinueWatching.mutate({
          id: movie.id,
          contentType: WatchDurationContentType.Movie,
          duration: Math.trunc(currentTime || 0),
          totalDuration: Math.ceil(totalDuration || 0),
        })
      }
    }, CONTINUE_WATCHING_INTERVAL)

    return () => clearInterval(progressInterval)
  }, [handleContinueWatching, movie])

  if (generating) {
    return <PageLoader />
  }

  if (isError) {
    return <PageError />
  }

  const generatedContent = data?.generateContentUrl
  const playbackToken = getTokenFromUrl(generatedContent?.url || "")

  const storyboardToken = getTokenFromUrl(generatedContent?.story_board_url || "")

  if (movie && generatedContent) {
    return (
      <div className="relative h-screen overflow-hidden">
        {movie && generatedContent ? (
          <MediaController className="size-full">
            <Button
              onClick={() => {
                handleBackButton().catch(console.error)
              }}
              variant="link"
              className="absolute left-4 top-4 z-50 size-12 rounded-full bg-black/30 hover:bg-black/50"
              type="button"
            >
              <LeftIcon className="scale-150" />
            </Button>
            <MuxPlayer
              // @ts-expect-error Muxplayer doesn't allow slot media but it works
              slot="media"
              ref={videoRef}
              playbackId={movie?.cdn_playback_id || ""}
              tokens={{
                playback: playbackToken || "",
                storyboard: storyboardToken || "",
              }}
              currentTime={movie?.continueWatching?.watched_duration}
              poster={
                movie?.imageLandscape?.path
                  ? `${baseUrl}/image/medium/${movie?.imageLandscape.path}`
                  : ""
              }
              autoPlay="any"
              className="mux-player-no-controls size-full"
              streamType="on-demand"
              onLoadedData={handlePlay}
            />
            {isLoading ? (
              <MediaLoadingIndicator
                suppressHydrationWarning
                noautohide
                className="z-40"
                slot="centered-chrome"
                style={{ "--media-loading-indicator-icon-height": "200px" }}
              ></MediaLoadingIndicator>
            ) : (
              <MediaPlayButton
                style={{
                  "--media-button-icon-height": "100px",
                  "--media-button-icon-width": "100px",
                }}
                className="z-40 size-full bg-transparent focus:outline-none"
                slot="centered-chrome"
              ></MediaPlayButton>
            )}
            <MediaSettingsMenu className="z-50" hidden={true} anchor="auto">
              <MediaSettingsMenuItem>
                Speed
                <MediaPlaybackRateMenu slot="submenu" hidden>
                  <div slot="title">Speed</div>
                </MediaPlaybackRateMenu>
              </MediaSettingsMenuItem>
              <MediaSettingsMenuItem>
                Quality
                <MediaRenditionMenu slot="submenu" hidden>
                  <div slot="title">Quality</div>
                </MediaRenditionMenu>
              </MediaSettingsMenuItem>
            </MediaSettingsMenu>
            <MediaCaptionsMenu className="z-50" hidden anchor="auto"></MediaCaptionsMenu>
            <MediaControlBar className="z-50">
              <MediaPlayButton></MediaPlayButton>
              <MediaSeekBackwardButton></MediaSeekBackwardButton>
              <MediaSeekForwardButton></MediaSeekForwardButton>
              <MediaMuteButton></MediaMuteButton>
              <MediaVolumeRange />
              <MediaTimeRange></MediaTimeRange>
              <MediaTimeDisplay showDuration></MediaTimeDisplay>
              <MediaCaptionsMenuButton></MediaCaptionsMenuButton>
              <MediaSettingsMenuButton></MediaSettingsMenuButton>
              <Button
                className="h-full rounded-none bg-[#14141E]/70"
                variant="link"
                onClick={() => void handleFullScreen()}
              >
                <MaximizeIcon />
              </Button>
            </MediaControlBar>
          </MediaController>
        ) : null}
      </div>
    )
  }
}

export default WatchMovie

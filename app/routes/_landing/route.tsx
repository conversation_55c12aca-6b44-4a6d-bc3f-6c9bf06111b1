import { cn } from "@/lib/utils/cn"
import { Link, Outlet, useLoaderData, useNavigate } from "react-router"
import SettingsMenu from "./layout/settings-menu"
import { useEffect, useState } from "react"
import { useUser } from "@/lib/hooks"
import Search from "./layout/search"
import RentLiveDialog from "./rent-live-dialog"
import { Footer, MobileLogin, PageError, PageLoader } from "@/components/common"
import { useGlobalLoadingStore } from "@/lib/store"
import { UAParser } from "ua-parser-js"

import type { Route } from "../_landing/+types/route"

export const meta = ({ location }: Route.MetaArgs) => {
  return [
    { title: "Enila" },
    {
      name: "description",
      content:
        "Enila is a user friendly streaming platform made with love in Aizawl, Mizoram",
    },
    {
      property: "og:title",
      content: `Enila`,
    },
    {
      property: "og:url",
      content: `https://enila.in${location.pathname}`,
    },
    {
      property: "og:type",
      content: "website",
    },
    {
      property: "og:image",
      itemProp: "image",
      content: "/enila_logo.png",
    },
    {
      property: "og:image:height",
      content: "600",
    },
    {
      property: "og:image:width",
      content: "600",
    },
    {
      property: "robots",
      content: "index,follow",
    },
    {
      property: "og:description",
      content:
        "Enila is a user friendly streaming platform made with love in Aizawl, Mizoram",
    },
    {
      property: "twitter:card",
      content: "summary_large_image",
    },
    {
      property: "twitter:url",
      content: `https://enila.in${location.pathname}`,
    },
    {
      property: "twitter:title",
      content: "Enila",
    },
    {
      property: "twitter:description",
      content:
        "Enila is a user friendly streaming platform made with love in Aizawl, Mizoram",
    },
    {
      property: "twitter:image",
      content: "/enila_logo.png",
    },
    {
      property: "og:site_name",
      content: "Enila",
    },
  ]
}

export const loader = ({ request }: Route.LoaderArgs) => {
  const userAgent = request.headers.get("User-Agent") || undefined
  const parser = new UAParser(userAgent)
  const deviceType = parser.getDevice()

  return { deviceType }
}

export default function Index() {
  const { deviceType } = useLoaderData<typeof loader>()
  const navigate = useNavigate()
  const [isScrolled, setIsScrolled] = useState(false)

  const [checkingDeviceType, setCheckingDeviceType] = useState(true)

  const { data, isLoading, isError } = useUser()

  const { globalLoading } = useGlobalLoadingStore()

  const handleScroll = () => {
    if (window.scrollY > 0) {
      setIsScrolled(true)
    } else {
      setIsScrolled(false)
    }
  }

  useEffect(() => {
    window.addEventListener("scroll", handleScroll)
    return () => {
      window.removeEventListener("scroll", handleScroll)
    }
  }, [])

  useEffect(() => {
    if (
      deviceType.type === "mobile" ||
      deviceType.type === "tablet" ||
      window.innerWidth < 1280
    ) {
      if (data?.getMe) {
        navigate("/m")
      }
    }
    setCheckingDeviceType(false)
  }, [deviceType.type, data, navigate])

  if (isLoading || globalLoading || checkingDeviceType) {
    return <PageLoader />
  }

  if (isError) {
    return <PageError />
  }

  return (
    <div className="flex h-full min-h-screen flex-col">
      <header>
        <nav
          className={cn(
            "fixed z-50 hidden h-[10vh] w-full items-center justify-between px-2 transition-colors duration-1000 ease-out lg:px-8 xl:flex",
            {
              "bg-black": isScrolled,
              "bg-transparent": !isScrolled,
            }
          )}
        >
          <Link
            to="/"
            className="flex h-full items-center gap-x-2 text-5xl font-bold text-theme-yellow"
          >
            <img
              alt="enila logo"
              className="mt-1 size-24"
              src="/enila_logo.png"
            />
            ENILA
          </Link>
          <div className="flex items-center space-x-2">
            <Search />
            <SettingsMenu />
          </div>
        </nav>
        <nav className="z-50 flex h-[10vh] w-full items-center justify-center px-2 xl:hidden">
          <Link
            to="/"
            className="flex items-center justify-center gap-x-2 text-4xl font-bold text-theme-yellow"
          >
            <img
              alt="enila logo"
              className="mt-1 size-12"
              src="/enila_logo.png"
            />
            EN ILA
          </Link>
        </nav>
      </header>
      <main className="hidden grow xl:block">
        <Outlet />
        <RentLiveDialog />
      </main>
      <main className="mx-auto size-full min-h-96 grow xl:hidden">
        <MobileLogin />
      </main>
      <Footer />
    </div>
  )
}
